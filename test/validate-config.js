#!/usr/bin/env node

/**
 * Configuration Validation Script
 * 
 * This script validates that the Jira API configuration is set up correctly
 * and can connect to your Jira instance.
 * 
 * Usage:
 *   node test/validate-config.js
 */

import { config, validateConfig } from './config.js';

async function makeJiraRequest(endpoint, options = {}) {
  const url = new URL(endpoint, config.JIRA_BASE_URL);
  
  const requestOptions = {
    method: options.method || 'GET',
    headers: {
      'Authorization': `Basic ${Buffer.from(`${config.JIRA_EMAIL}:${config.JIRA_ACCESS_TOKEN}`).toString('base64')}`,
      'Accept': 'application/json',
      'Content-Type': 'application/json',
      ...options.headers
    }
  };

  const response = await fetch(url.toString(), requestOptions);
  
  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`HTTP ${response.status}: ${response.statusText} - ${errorText}`);
  }

  return await response.json();
}

async function validateConnection() {
  console.log('🔍 Validating Jira API connection...');
  
  try {
    // Test basic API access
    const myself = await makeJiraRequest('/rest/api/3/myself');
    console.log(`✅ Connected to Jira as: ${myself.displayName} (${myself.emailAddress})`);
    
    // Test issue access
    console.log(`🔍 Checking access to test issue ${config.TEST_ISSUE_ID}...`);
    const issue = await makeJiraRequest(`/rest/api/3/issue/${config.TEST_ISSUE_ID}`);
    console.log(`✅ Can access issue: ${issue.key} - ${issue.fields.summary}`);
    
    // Test field access
    console.log('🔍 Checking custom fields...');
    const fields = await makeJiraRequest('/rest/api/3/field');
    
    const simpleReqField = fields.find(f => f.name === 'Simple Requirements');
    const fullReqField = fields.find(f => f.name === 'Full Requirements');
    
    if (simpleReqField) {
      console.log(`✅ Found Simple Requirements field: ${simpleReqField.id}`);
    } else {
      console.log('⚠️  Simple Requirements field not found');
    }
    
    if (fullReqField) {
      console.log(`✅ Found Full Requirements field: ${fullReqField.id}`);
    } else {
      console.log('⚠️  Full Requirements field not found');
    }
    
    console.log('');
    console.log('🎉 Configuration validation completed successfully!');
    console.log('   You can now run the integration tests.');
    
  } catch (error) {
    console.error('❌ Configuration validation failed:');
    console.error(`   ${error.message}`);
    console.error('');
    console.error('💡 Troubleshooting tips:');
    console.error('   - Check your JIRA_BASE_URL is correct');
    console.error('   - Verify your JIRA_ACCESS_TOKEN is valid');
    console.error('   - Ensure your JIRA_EMAIL matches the token owner');
    console.error('   - Make sure the test issue exists and you have access');
    process.exit(1);
  }
}

async function main() {
  console.log('🚀 Jira API Configuration Validator');
  console.log('');
  
  try {
    // Validate configuration
    validateConfig();
    console.log('');
    
    // Test connection
    await validateConnection();
    
  } catch (error) {
    console.error('❌ Validation failed:', error.message);
    process.exit(1);
  }
}

main().catch(console.error);
