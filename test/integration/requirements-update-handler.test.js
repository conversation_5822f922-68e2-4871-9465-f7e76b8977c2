import { test, describe, before, after } from 'node:test';
import assert from 'node:assert';
import { config, validateConfig } from '../config.js';

describe('RequirementsUpdateHandler Integration Test', () => {
  let simpleReqFieldId = null;
  let fullReqFieldId = null;
  let originalSimpleValue = null;
  let originalFullValue = null;

  // Helper function to make authenticated Jira API requests
  async function makeJiraRequest(endpoint, options = {}) {
    const url = new URL(endpoint, config.JIRA_BASE_URL);

    const requestOptions = {
      method: options.method || 'GET',
      headers: {
        'Authorization': `Basic ${Buffer.from(`${config.JIRA_EMAIL}:${config.JIRA_ACCESS_TOKEN}`).toString('base64')}`,
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        ...options.headers
      }
    };

    if (options.body) {
      requestOptions.body = typeof options.body === 'string' ? options.body : JSON.stringify(options.body);
    }

    try {
      const response = await fetch(url.toString(), requestOptions);

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${response.statusText} - ${errorText}`);
      }

      // Handle empty responses (like from PUT requests)
      const contentType = response.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        return await response.json();
      }

      return null;
    } catch (error) {
      throw new Error(`Jira API request failed: ${error.message}`);
    }
  }

  // Helper function to get custom field ID by name
  async function getCustomFieldId(fieldName) {
    try {
      const fieldsData = await makeJiraRequest('/rest/api/3/field');
      const foundField = fieldsData.find(f => f.name === fieldName);

      if (!foundField) {
        throw new Error(`Could not find custom field with name: ${fieldName}`);
      }

      console.log(`Found field ${fieldName} with ID: ${foundField.id}, Type: ${foundField.schema?.type}, Custom: ${foundField.schema?.custom}`);
      return foundField.id;
    } catch (error) {
      throw new Error(`Error fetching custom field ID for ${fieldName}: ${error.message}`);
    }
  }

  // Helper function to get issue details
  async function getIssue(issueId) {
    try {
      return await makeJiraRequest(`/rest/api/3/issue/${issueId}`);
    } catch (error) {
      throw new Error(`Error fetching issue ${issueId}: ${error.message}`);
    }
  }

  // Helper function to update issue fields
  async function updateIssue(issueId, fields) {
    try {
      await makeJiraRequest(`/rest/api/3/issue/${issueId}`, {
        method: 'PUT',
        body: { fields }
      });
    } catch (error) {
      throw new Error(`Error updating issue ${issueId}: ${error.message}`);
    }
  }

  // Helper function to wait for a specified number of seconds
  function wait(seconds) {
    return new Promise(resolve => setTimeout(resolve, seconds * 1000));
  }

  before(async () => {
    console.log('Setting up integration test...');

    // Validate configuration
    validateConfig();

    // Get field IDs
    simpleReqFieldId = await getCustomFieldId('Simple Requirements');
    fullReqFieldId = await getCustomFieldId('Full Requirements');
    
    console.log(`Simple Requirements Field ID: ${simpleReqFieldId}`);
    console.log(`Full Requirements Field ID: ${fullReqFieldId}`);

    // Get current issue state to restore later
    const issue = await getIssue(config.TEST_ISSUE_ID);
    originalSimpleValue = issue.fields[simpleReqFieldId];
    originalFullValue = issue.fields[fullReqFieldId];
    
    console.log(`Original Simple Requirements: ${originalSimpleValue}`);
    console.log(`Original Full Requirements: ${JSON.stringify(originalFullValue)}`);
  });

  after(async () => {
    console.log('Cleaning up integration test...');
    
    // Restore original values
    const restoreFields = {};
    if (originalSimpleValue !== null) {
      restoreFields[simpleReqFieldId] = originalSimpleValue;
    }
    if (originalFullValue !== null) {
      restoreFields[fullReqFieldId] = originalFullValue;
    }
    
    if (Object.keys(restoreFields).length > 0) {
      await updateIssue(config.TEST_ISSUE_ID, restoreFields);
      console.log('Original field values restored');
    }
  });

  test('should update FullRequirements field when SimpleRequirements field is updated', async () => {
    console.log('Starting integration test...');
    
    // Step 1: Clear the FullRequirements field and set SimpleRequirements field
    const testSimpleRequirements = 'Test requirement: Create a user login screen';
    const updateFields = {
      [simpleReqFieldId]: testSimpleRequirements,
      [fullReqFieldId]: null // Clear the full requirements field
    };
    
    console.log('Step 1: Updating SimpleRequirements field and clearing FullRequirements field...');
    await updateIssue(config.TEST_ISSUE_ID, updateFields);

    // Verify the update was successful
    let issue = await getIssue(config.TEST_ISSUE_ID);
    assert.strictEqual(
      issue.fields[simpleReqFieldId], 
      testSimpleRequirements, 
      'SimpleRequirements field should be updated'
    );
    assert.strictEqual(
      issue.fields[fullReqFieldId], 
      null, 
      'FullRequirements field should be cleared'
    );
    
    console.log('Step 2: Waiting 20 seconds for the handler to process the update...');
    await wait(20);
    
    // Step 3: Check if FullRequirements field was updated
    console.log('Step 3: Checking if FullRequirements field was updated...');
    issue = await getIssue(config.TEST_ISSUE_ID);
    
    const updatedFullRequirements = issue.fields[fullReqFieldId];
    console.log(`Updated Full Requirements: ${JSON.stringify(updatedFullRequirements)}`);
    
    // Verify that the FullRequirements field was updated with content
    assert.notStrictEqual(
      updatedFullRequirements, 
      null, 
      'FullRequirements field should not be null after processing'
    );
    
    // For ADF content, check if it has the expected structure
    if (updatedFullRequirements && typeof updatedFullRequirements === 'object') {
      
      // Check if there's actual content (not just empty structure)
      if (updatedFullRequirements.content && Array.isArray(updatedFullRequirements.content)) {
        assert.ok(
          updatedFullRequirements.content.length > 0,
          'FullRequirements field should contain non-empty content'
        );
      }
    } else if (typeof updatedFullRequirements === 'string') {
      assert.ok(
        updatedFullRequirements.trim().length > 0,
        'FullRequirements field should contain non-empty string content'
      );
    }
    
    console.log('Integration test completed successfully!');
  });
});
