import { test, describe, before, after } from 'node:test';
import assert from 'node:assert';
import api, { route } from '@forge/api';

describe('RequirementsUpdateHandler Integration Test', () => {
  const TEST_ISSUE_ID = '10139';
  let simpleReqFieldId = null;
  let fullReqFieldId = null;
  let originalSimpleValue = null;
  let originalFullValue = null;

  // Helper to get custom field ID - uses API fallback since context may not have field info
  const getCustomFieldId = async (fieldKey) => {
    try {
      // Query the API to get all fields and find our custom fields
      const fieldsResponse = await api.asApp().requestJira(route`/rest/api/3/field`);
      if (!fieldsResponse.ok) {
        console.error(`Failed to fetch fields: ${fieldsResponse.status} ${fieldsResponse.statusText}`);
        return null;
      }

      const fieldsData = await fieldsResponse.json();

      // Map field keys to field names
      const fieldNameMap = {
        'simple-requirements-field': 'Simple Requirements',
        'full-requirements-field': 'Full Requirements'
      };

      const targetFieldName = fieldNameMap[fieldKey];
      if (!targetFieldName) {
        console.error(`Unknown field key: ${fieldKey}`);
        return null;
      }

      // Find the field by name
      const foundField = fieldsData.find(f => f.name === targetFieldName);
      if (!foundField) {
        console.error(`Could not find custom field with name: ${targetFieldName}`);
        return null;
      }

      console.log(`Found field ${targetFieldName} with ID: ${foundField.id}, Type: ${foundField.schema?.type}, Custom: ${foundField.schema?.custom}`);
      return foundField;
    } catch (error) {
      console.error(`Error fetching custom field ID for ${fieldKey}:`, error);
      return null;
    }
  };

  // Helper function to get issue details
  async function getIssue(issueId) {
    try {
      const response = await api.asApp().requestJira(route`/rest/api/3/issue/${issueId}`);
      if (!response.ok) {
        throw new Error(`Failed to fetch issue: ${response.status} ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      throw new Error(`Error fetching issue ${issueId}: ${error.message}`);
    }
  }

  // Helper function to update issue fields
  async function updateIssue(issueId, fields) {
    try {
      const response = await api.asApp().requestJira(route`/rest/api/3/issue/${issueId}`, {
        method: 'PUT',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ fields })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to update issue: ${response.status} ${response.statusText} - ${errorText}`);
      }
    } catch (error) {
      throw new Error(`Error updating issue ${issueId}: ${error.message}`);
    }
  }

  // Helper function to wait for a specified number of seconds
  function wait(seconds) {
    return new Promise(resolve => setTimeout(resolve, seconds * 1000));
  }

  before(async () => {
    console.log('Setting up integration test...');
    
    // Get field IDs
    simpleReqFieldId = await getCustomFieldId('Simple Requirements');
    fullReqFieldId = await getCustomFieldId('Full Requirements');
    
    console.log(`Simple Requirements Field ID: ${simpleReqFieldId}`);
    console.log(`Full Requirements Field ID: ${fullReqFieldId}`);

    // Get current issue state to restore later
    const issue = await getIssue(TEST_ISSUE_ID);
    originalSimpleValue = issue.fields[simpleReqFieldId];
    originalFullValue = issue.fields[fullReqFieldId];
    
    console.log(`Original Simple Requirements: ${originalSimpleValue}`);
    console.log(`Original Full Requirements: ${JSON.stringify(originalFullValue)}`);
  });

  after(async () => {
    console.log('Cleaning up integration test...');
    
    // Restore original values
    const restoreFields = {};
    if (originalSimpleValue !== null) {
      restoreFields[simpleReqFieldId] = originalSimpleValue;
    }
    if (originalFullValue !== null) {
      restoreFields[fullReqFieldId] = originalFullValue;
    }
    
    if (Object.keys(restoreFields).length > 0) {
      await updateIssue(TEST_ISSUE_ID, restoreFields);
      console.log('Original field values restored');
    }
  });

  test('should update FullRequirements field when SimpleRequirements field is updated', async () => {
    console.log('Starting integration test...');
    
    // Step 1: Clear the FullRequirements field and set SimpleRequirements field
    const testSimpleRequirements = 'Test requirement: Create a user login screen';
    const updateFields = {
      [simpleReqFieldId]: testSimpleRequirements,
      [fullReqFieldId]: null // Clear the full requirements field
    };
    
    console.log('Step 1: Updating SimpleRequirements field and clearing FullRequirements field...');
    await updateIssue(TEST_ISSUE_ID, updateFields);
    
    // Verify the update was successful
    let issue = await getIssue(TEST_ISSUE_ID);
    assert.strictEqual(
      issue.fields[simpleReqFieldId], 
      testSimpleRequirements, 
      'SimpleRequirements field should be updated'
    );
    assert.strictEqual(
      issue.fields[fullReqFieldId], 
      null, 
      'FullRequirements field should be cleared'
    );
    
    console.log('Step 2: Waiting 20 seconds for the handler to process the update...');
    await wait(20);
    
    // Step 3: Check if FullRequirements field was updated
    console.log('Step 3: Checking if FullRequirements field was updated...');
    issue = await getIssue(TEST_ISSUE_ID);
    
    const updatedFullRequirements = issue.fields[fullReqFieldId];
    console.log(`Updated Full Requirements: ${JSON.stringify(updatedFullRequirements)}`);
    
    // Verify that the FullRequirements field was updated with content
    assert.notStrictEqual(
      updatedFullRequirements, 
      null, 
      'FullRequirements field should not be null after processing'
    );
    
    // For ADF content, check if it has the expected structure
    if (updatedFullRequirements && typeof updatedFullRequirements === 'object') {
      
      // Check if there's actual content (not just empty structure)
      if (updatedFullRequirements.content && Array.isArray(updatedFullRequirements.content)) {
        assert.ok(
          updatedFullRequirements.content.length > 0,
          'FullRequirements field should contain non-empty content'
        );
      }
    } else if (typeof updatedFullRequirements === 'string') {
      assert.ok(
        updatedFullRequirements.trim().length > 0,
        'FullRequirements field should contain non-empty string content'
      );
    }
    
    console.log('Integration test completed successfully!');
  });
});
