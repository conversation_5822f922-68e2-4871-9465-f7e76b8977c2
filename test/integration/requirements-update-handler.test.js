import { test, describe, before, after } from 'node:test';
import assert from 'node:assert';
import { config, validateConfig } from '../config.js';

describe('RequirementsUpdateHandler Integration Test', () => {
  let simpleReqFieldId = null;
  let fullReqFieldId = null;
  let originalSimpleValue = null;
  let originalFullValue = null;

  // Helper function to make authenticated Jira API requests
  async function makeJiraRequest(endpoint, options = {}) {
    const url = new URL(endpoint, config.JIRA_BASE_URL);

    const requestOptions = {
      method: options.method || 'GET',
      headers: {
        'Authorization': `Basic ${Buffer.from(`${config.JIRA_EMAIL}:${config.JIRA_ACCESS_TOKEN}`).toString('base64')}`,
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        ...options.headers
      }
    };

    if (options.body) {
      requestOptions.body = typeof options.body === 'string' ? options.body : JSON.stringify(options.body);
    }

    try {
      const response = await fetch(url.toString(), requestOptions);

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${response.statusText} - ${errorText}`);
      }

      // Handle empty responses (like from PUT requests)
      const contentType = response.headers.get('content-type');
      const contentLength = response.headers.get('content-length');

      // If there's no content or content-length is 0, return null
      if (contentLength === '0' || response.status === 204) {
        return null;
      }

      // If there's JSON content, parse it
      if (contentType && contentType.includes('application/json')) {
        const text = await response.text();
        return text ? JSON.parse(text) : null;
      }

      return null;
    } catch (error) {
      throw new Error(`Jira API request failed: ${error.message}`);
    }
  }

  // Helper function to get custom field ID by name
  async function getCustomFieldId(fieldName) {
    try {
      const fieldsData = await makeJiraRequest('/rest/api/3/field');
      const foundField = fieldsData.find(f => f.name === fieldName);

      if (!foundField) {
        throw new Error(`Could not find custom field with name: ${fieldName}`);
      }

      console.log(`Found field ${fieldName} with ID: ${foundField.id}, Type: ${foundField.schema?.type}, Custom: ${foundField.schema?.custom}`);
      return foundField.id;
    } catch (error) {
      throw new Error(`Error fetching custom field ID for ${fieldName}: ${error.message}`);
    }
  }

  // Helper function to get issue details
  async function getIssue(issueId) {
    try {
      return await makeJiraRequest(`/rest/api/3/issue/${issueId}`);
    } catch (error) {
      throw new Error(`Error fetching issue ${issueId}: ${error.message}`);
    }
  }

  // Helper function to update issue fields
  async function updateIssue(issueId, fields) {
    try {
      await makeJiraRequest(`/rest/api/3/issue/${issueId}`, {
        method: 'PUT',
        body: { fields }
      });
    } catch (error) {
      throw new Error(`Error updating issue ${issueId}: ${error.message}`);
    }
  }

  // Helper function to wait for a specified number of seconds
  function wait(seconds) {
    return new Promise(resolve => setTimeout(resolve, seconds * 1000));
  }

  before(async () => {
    console.log('Setting up integration test...');

    // Validate configuration
    validateConfig();

    // Get field IDs
    simpleReqFieldId = await getCustomFieldId('Simple Requirements');
    fullReqFieldId = await getCustomFieldId('Full Requirements');
    
    console.log(`Simple Requirements Field ID: ${simpleReqFieldId}`);
    console.log(`Full Requirements Field ID: ${fullReqFieldId}`);

    // Get current issue state to restore later
    const issue = await getIssue(config.TEST_ISSUE_ID);
    originalSimpleValue = issue.fields[simpleReqFieldId];
    originalFullValue = issue.fields[fullReqFieldId];
    
    console.log(`Original Simple Requirements: ${originalSimpleValue}`);
    console.log(`Original Full Requirements: ${JSON.stringify(originalFullValue)}`);
  });

  after(async () => {
    console.log('Cleaning up integration test...');
    
    // Restore original values
    const restoreFields = {};
    if (originalSimpleValue !== null) {
      restoreFields[simpleReqFieldId] = originalSimpleValue;
    }
    if (originalFullValue !== null) {
      restoreFields[fullReqFieldId] = originalFullValue;
    }
    
    if (Object.keys(restoreFields).length > 0) {
      await updateIssue(config.TEST_ISSUE_ID, restoreFields);
      console.log('Original field values restored');
    }
  });

  test('should update FullRequirements field when SimpleRequirements field is updated', async () => {
    console.log('Starting integration test...');
    
    // Step 1: Clear the FullRequirements field and set SimpleRequirements field
    const testSimpleRequirements = 'Test requirement: Create a user login system with authentication';
    const updateFields = {
      [simpleReqFieldId]: testSimpleRequirements,
      [fullReqFieldId]: null // Clear the full requirements field
    };

    console.log(`Test input: "${testSimpleRequirements}"`);
    console.log('Expected: FullRequirements field should be populated by the handler');
    
    console.log('Step 1: Updating SimpleRequirements field and clearing FullRequirements field...');
    await updateIssue(config.TEST_ISSUE_ID, updateFields);

    // Verify the update was successful
    let issue = await getIssue(config.TEST_ISSUE_ID);
    assert.strictEqual(
      issue.fields[simpleReqFieldId], 
      testSimpleRequirements, 
      'SimpleRequirements field should be updated'
    );
    assert.strictEqual(
      issue.fields[fullReqFieldId], 
      null, 
      'FullRequirements field should be cleared'
    );
    
    console.log('Step 2: Waiting for the handler to process the update...');
    console.log('   This may take up to 60 seconds...');

    // Poll for changes every 5 seconds for up to 60 seconds
    let attempts = 0;
    const maxAttempts = 12; // 12 * 5 = 60 seconds
    let updatedFullRequirements = null;

    while (attempts < maxAttempts) {
      await wait(5);
      attempts++;

      console.log(`   Checking attempt ${attempts}/${maxAttempts}...`);
      issue = await getIssue(config.TEST_ISSUE_ID);
      updatedFullRequirements = issue.fields[fullReqFieldId];

      if (updatedFullRequirements !== null) {
        console.log(`   ✅ FullRequirements field updated after ${attempts * 5} seconds!`);
        break;
      }
    }

    if (attempts >= maxAttempts) {
      console.log('   ⚠️  FullRequirements field still null after 60 seconds');
      console.log('');
      console.log('🔍 Debugging Information:');
      console.log('   This could indicate:');
      console.log('   1. The Forge app trigger is not activated by API updates');
      console.log('   2. The RequirementsUpdateHandler is not processing the event');
      console.log('   3. The OpenRouter API key is not configured correctly');
      console.log('   4. The handler is encountering an error');
      console.log('   5. The handler takes longer than 60 seconds to process');
      console.log('');
      console.log('💡 Troubleshooting steps:');
      console.log('   1. Check Forge app logs: `forge logs`');
      console.log('   2. Verify OpenRouter API key is set: `forge variables list`');
      console.log('   3. Try updating the field manually in Jira UI');
      console.log('   4. Check if the trigger works with UI updates vs API updates');
    }
    
    // Step 3: Final verification
    console.log('Step 3: Final verification of FullRequirements field...');
    console.log(`Final Full Requirements: ${JSON.stringify(updatedFullRequirements)}`);
    
    // Verify that the FullRequirements field was updated with content
    assert.notStrictEqual(
      updatedFullRequirements, 
      null, 
      'FullRequirements field should not be null after processing'
    );
    
    // For ADF content, check if it has the expected structure
    if (updatedFullRequirements && typeof updatedFullRequirements === 'object') {
      
      // Check if there's actual content (not just empty structure)
      if (updatedFullRequirements.content && Array.isArray(updatedFullRequirements.content)) {
        assert.ok(
          updatedFullRequirements.content.length > 0,
          'FullRequirements field should contain non-empty content'
        );
      }
    } else if (typeof updatedFullRequirements === 'string') {
      assert.ok(
        updatedFullRequirements.trim().length > 0,
        'FullRequirements field should contain non-empty string content'
      );
    }
    
    console.log('Integration test completed successfully!');
  });
});
