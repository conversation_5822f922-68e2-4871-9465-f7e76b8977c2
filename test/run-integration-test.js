#!/usr/bin/env node

/**
 * Integration Test Runner for RequirementsUpdateHandler
 * 
 * This script runs the integration test for the RequirementsUpdateHandler.
 * It requires the Forge app to be deployed and running.
 * 
 * Usage:
 *   node test/run-integration-test.js
 *   
 * Or via npm:
 *   npm run test:integration
 */

import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

console.log('🚀 Starting RequirementsUpdateHandler Integration Test');
console.log('');
console.log('Prerequisites:');
console.log('- Forge app must be deployed and running');
console.log('- Issue 10139 must exist in your Jira instance');
console.log('- Both Simple Requirements and Full Requirements custom fields must be available');
console.log('- OpenRouter API key must be configured');
console.log('');

const testFile = join(__dirname, 'integration', 'requirements-update-handler.test.js');

const testProcess = spawn('node', ['--test', testFile], {
  stdio: 'inherit',
  env: {
    ...process.env,
    NODE_OPTIONS: '--experimental-modules'
  }
});

testProcess.on('close', (code) => {
  if (code === 0) {
    console.log('');
    console.log('✅ Integration test completed successfully!');
  } else {
    console.log('');
    console.log('❌ Integration test failed with exit code:', code);
    process.exit(code);
  }
});

testProcess.on('error', (error) => {
  console.error('❌ Failed to start test process:', error);
  process.exit(1);
});
